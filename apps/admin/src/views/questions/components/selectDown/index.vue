<script lang="ts" setup>
import { computed, ref, watch } from 'vue'

defineOptions({
  name: 'SelectDrawer',
})

const props = defineProps<Props>()

const emit = defineEmits<{
  confirm: [selectedChapters: string[]]
}>()

interface Props {
  title: string
  chapterOptions: QuestionsApi.GetChapterListResponse[]
}

const isShowModal = defineModel('isShowModal', {
  type: Boolean,
  default: false,
})

// 搜索关键词
const searchKeyword = ref('')

// 选中的章节ID列表
const selectedChapterIds = ref<string[]>([])

// 树形数据转换
const treeData = computed(() => {
  return props.chapterOptions.map(chapter => ({
    key: chapter.ChapterId,
    label: chapter.ChapterName,
    children: chapter.Second?.map(subChapter => ({
      key: subChapter.ChapterId,
      label: subChapter.ChapterName,
      isLeaf: true,
    })) || [],
  }))
})

// 过滤后的树形数据
const filteredTreeData = computed(() => {
  if (!searchKeyword.value.trim()) {
    return treeData.value
  }

  const keyword = searchKeyword.value.toLowerCase().trim()

  return treeData.value.filter((chapter) => {
    // 检查父级章节名称是否匹配
    const parentMatch = chapter.label.toLowerCase().includes(keyword)

    // 检查子级章节是否有匹配的
    const childrenMatch = chapter.children?.some(child =>
      child.label.toLowerCase().includes(keyword),
    )

    // 如果父级匹配，保留所有子级；如果只有子级匹配，只保留匹配的子级
    if (parentMatch) {
      return true
    }
    else if (childrenMatch) {
      chapter.children = chapter.children?.filter(child =>
        child.label.toLowerCase().includes(keyword),
      )
      return true
    }

    return false
  })
})

// 处理取消
function handleCancel() {
  isShowModal.value = false
  selectedChapterIds.value = []
  searchKeyword.value = ''
}

// 处理确定
function handleConfirm() {
  emit('confirm', selectedChapterIds.value)
  isShowModal.value = false
  selectedChapterIds.value = []
  searchKeyword.value = ''
}

// 监听抽屉关闭，重置状态
watch(isShowModal, (newVal) => {
  if (!newVal) {
    selectedChapterIds.value = []
    searchKeyword.value = ''
  }
})
</script>

<template>
  <NDrawer v-model:show="isShowModal" width="50%" :auto-focus="false">
    <NDrawerContent :title="props.title" closable :native-scrollbar="false">
      <!-- 搜索框 -->
      <div class="mb-16px">
        <NInput
          v-model:value="searchKeyword"
          placeholder="搜索"
          clearable
          class="w-full"
        >
          <template #prefix>
            <icon-ic-round-search class="text-16px text-gray-400" />
          </template>
        </NInput>
      </div>

      <!-- 已选择数量显示 -->
      <div class="mb-16px flex items-center justify-between">
        <span class="text-14px text-gray-600">
          已选 {{ selectedChapterIds.length }} 个知识点
        </span>
      </div>

      <!-- 章节树形列表 -->
      <div class="flex-1 overflow-hidden">
        <NTree
          v-model:checked-keys="selectedChapterIds"
          :data="filteredTreeData"
          key-field="key"
          label-field="label"
          children-field="children"
          checkable
          block-line
          expand-on-click
          virtual-scroll
          class="h-full"
          :default-expanded-keys="filteredTreeData.map(item => item.key)"
        />
      </div>

      <template #footer>
        <NSpace>
          <NButton @click="handleCancel">
            取消
          </NButton>
          <NButton type="primary" @click="handleConfirm">
            确定
          </NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
:deep(.n-tree-node-content) {
  padding: 8px 0;
}

:deep(.n-tree-node-content__text) {
  font-size: 14px;
}

:deep(.n-tree-node--checkable .n-tree-node-content) {
  padding-left: 8px;
}
</style>
