<script lang="ts" setup>
import { computed, ref, watch } from 'vue'

defineOptions({
  name: 'SelectDrawer',
})

const props = defineProps<Props>()

const emit = defineEmits<{
  confirm: [selectedChapters: string[]]
}>()

interface Props {
  title: string
  chapterOptions: QuestionsApi.GetChapterListResponse[]
}

const isShowModal = defineModel('isShowModal', {
  type: Boolean,
  default: false,
})

// 搜索关键词
const searchKeyword = ref('')

// 选中的章节ID列表
const selectedChapterIds = ref<string[]>([])

// 树形数据转换
const treeData = computed(() => {
  return props.chapterOptions.map(chapter => ({
    key: chapter.ChapterId,
    label: chapter.ChapterName,
    children: chapter.Second?.map(subChapter => ({
      key: subChapter.ChapterId,
      label: subChapter.ChapterName,
      isLeaf: true,
    })) || [],
  }))
})

// 过滤后的树形数据
const filteredTreeData = computed(() => {
  if (!searchKeyword.value.trim()) {
    return treeData.value
  }

  const keyword = searchKeyword.value.toLowerCase().trim()

  return treeData.value.filter((chapter) => {
    // 检查父级章节名称是否匹配
    const parentMatch = chapter.label.toLowerCase().includes(keyword)

    // 检查子级章节是否有匹配的
    const childrenMatch = chapter.children?.some(child =>
      child.label.toLowerCase().includes(keyword),
    )

    // 如果父级匹配，保留所有子级；如果只有子级匹配，只保留匹配的子级
    if (parentMatch) {
      return true
    }
    else if (childrenMatch) {
      chapter.children = chapter.children?.filter(child =>
        child.label.toLowerCase().includes(keyword),
      )
      return true
    }

    return false
  })
})

// 获取所有节点的扁平化数据（包括父级和子级）
const allFlatNodes = computed(() => {
  const nodes: Array<{ key: string, label: string }> = []

  treeData.value.forEach((chapter) => {
    nodes.push({ key: chapter.key, label: chapter.label })
    if (chapter.children) {
      chapter.children.forEach((child) => {
        nodes.push({ key: child.key, label: child.label })
      })
    }
  })

  return nodes
})

// 已选择的章节名称列表
const selectedChapterNames = computed(() => {
  return selectedChapterIds.value
    .map(id => allFlatNodes.value.find(node => node.key === id))
    .filter(Boolean) as Array<{ key: string, label: string }>
})

// 移除已选择的项目
function removeSelectedItem(key: string) {
  const index = selectedChapterIds.value.indexOf(key)
  if (index > -1) {
    selectedChapterIds.value.splice(index, 1)
  }
}

// 处理取消
function handleCancel() {
  isShowModal.value = false
  selectedChapterIds.value = []
  searchKeyword.value = ''
}

// 处理确定
function handleConfirm() {
  emit('confirm', selectedChapterIds.value)
  isShowModal.value = false
  selectedChapterIds.value = []
  searchKeyword.value = ''
}

// 监听抽屉关闭，重置状态
watch(isShowModal, (newVal) => {
  if (!newVal) {
    selectedChapterIds.value = []
    searchKeyword.value = ''
  }
})
</script>

<template>
  <NDrawer v-model:show="isShowModal" width="50%" :auto-focus="false">
    <NDrawerContent :title="props.title" closable :native-scrollbar="false">
      <!-- 搜索框 -->
      <div class="mb-16px">
        <NInput
          v-model:value="searchKeyword"
          placeholder="搜索"
          clearable
          class="w-full"
        >
          <template #prefix>
            <icon-ic-round-search class="text-16px text-gray-400" />
          </template>
        </NInput>
      </div>

      <!-- 主要内容区域 -->
      <div class="h-full flex gap-16px">
        <!-- 左侧：章节树形列表 -->
        <div class="flex flex-col flex-1">
          <div class="mb-12px text-14px text-gray-600">
            已选 {{ selectedChapterIds.length }} 个知识点
          </div>

          <div class="flex-1 overflow-hidden border border-gray-200 rounded-6px">
            <NTree
              v-model:checked-keys="selectedChapterIds"
              :data="filteredTreeData"
              :show-line="showLine"
              key-field="key"
              label-field="label"
              children-field="children"
              checkable
              block-line
              expand-on-click
              virtual-scroll
              :default-expanded-keys="filteredTreeData.map(item => item.key)"
            />
          </div>
        </div>

        <!-- 右侧：已选择的项目列表 -->
        <div class="w-280px flex flex-col">
          <div class="mb-12px text-14px text-gray-600">
            已选择的知识点
          </div>

          <div class="flex-1 overflow-hidden border border-gray-200 rounded-6px bg-gray-50">
            <div class="h-full overflow-y-auto p-8px">
              <div v-if="selectedChapterNames.length === 0" class="h-full flex items-center justify-center text-gray-400">
                暂无选择
              </div>
              <div v-else class="space-y-6px">
                <div
                  v-for="item in selectedChapterNames"
                  :key="item.key"
                  class="flex items-center justify-between rounded-4px bg-white p-8px shadow-sm"
                >
                  <span class="flex-1 truncate text-13px text-gray-700" :title="item.label">
                    {{ item.label }}
                  </span>
                  <NButton
                    size="tiny"
                    quaternary
                    type="error"
                    @click="removeSelectedItem(item.key)"
                  >
                    <template #icon>
                      <icon-ic-round-close class="text-12px" />
                    </template>
                  </NButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <NSpace>
          <NButton @click="handleCancel">
            取消
          </NButton>
          <NButton type="primary" @click="handleConfirm">
            确定
          </NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped>
:deep(.n-tree-node-content) {
  padding: 8px 0;
}

:deep(.n-tree-node-content__text) {
  font-size: 14px;
}

:deep(.n-tree-node--checkable .n-tree-node-content) {
  padding-left: 8px;
}
</style>
